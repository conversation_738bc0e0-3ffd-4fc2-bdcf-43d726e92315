{% extends "base.html" %}

{% block title %}Mode Formulaire - Approvisionnement{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
<script src="{{ url_for('inventory.static', filename='js/stock_replenishment_form.js') }}"></script>
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête Mode Formulaire -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-edit"></i> Mode Formulaire - Approvisionnement
                </h4>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.stock_replenishment_pos_mode') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-cash-register"></i> Mode POS
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Colonne gauche - Formulaire -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plus-circle"></i> Ajouter des Articles à la Commande</h5>
                    </div>
                    <div class="card-body">
                        <form id="stockReplenishmentForm">
                            {{ form.hidden_tag() }}
                            
                            <!-- Sélection du fournisseur -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.supplier_category_id.label }}</label>
                                    {{ form.supplier_category_id(class="form-select", id="supplierCategorySelect") }}
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.supplier_id.label }}</label>
                                    {{ form.supplier_id(class="form-select", id="supplierSelect") }}
                                </div>
                            </div>

                            <!-- Type d'article -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.item_type.label }}</label>
                                    {{ form.item_type(class="form-select", id="itemTypeSelect") }}
                                </div>
                                <div class="col-md-6">
                                    <!-- Sélection de l'article (dynamique) -->
                                    <label class="form-label" id="itemSelectLabel">Article</label>
                                    <div id="productSelectDiv" style="display: none;">
                                        {{ form.product_id(class="form-select", id="productSelect") }}
                                    </div>
                                    <div id="ingredientSelectDiv" style="display: none;">
                                        {{ form.ingredient_id(class="form-select", id="ingredientSelect") }}
                                    </div>
                                </div>
                            </div>

                            <!-- Quantité et prix -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label">{{ form.quantity.label }}</label>
                                    {{ form.quantity(class="form-control", step="0.01", min="0.01") }}
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">{{ form.unit_price.label }}</label>
                                    {{ form.unit_price(class="form-control", step="0.01", min="0") }}
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Total</label>
                                    <input type="text" class="form-control" id="totalPrice" readonly>
                                </div>
                            </div>

                            <!-- Date de livraison -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ form.expected_delivery_date.label }}</label>
                                    {{ form.expected_delivery_date(class="form-control", type="datetime-local") }}
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mt-4">
                                        {{ form.is_order_only(class="form-check-input") }}
                                        <label class="form-check-label" for="{{ form.is_order_only.id }}">
                                            {{ form.is_order_only.label.text }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                <label class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes(class="form-control", rows="3") }}
                            </div>

                            <!-- Boutons d'action -->
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                    <i class="fas fa-undo"></i> Réinitialiser
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Ajouter à la commande
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Informations sur l'article sélectionné -->
                <div class="card mt-3" id="itemInfoCard" style="display: none;">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> Informations sur l'article</h6>
                    </div>
                    <div class="card-body" id="itemInfoContent">
                        <!-- Contenu dynamique -->
                    </div>
                </div>
            </div>

            <!-- Colonne droite - Ticket d'achat -->
            <div class="col-lg-4">
                <!-- Ticket d'achat -->
                <div class="purchase-ticket">
                    <div class="ticket-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-receipt me-2"></i>Ticket d'Achat
                            </h6>
                            <span id="ticketNumberForm" class="badge bg-light text-dark">
                                #{{ now.strftime('%y%m%d%H%M%S') }}
                            </span>
                        </div>
                        <div class="ticket-info mt-2">
                            <small id="supplierInfoForm">
                                <i class="fas fa-truck me-1"></i>Fournisseur: Autres
                            </small><br>
                            <small>
                                <i class="fas fa-user me-1"></i>Utilisateur: {{ current_user.username }}
                            </small><br>
                            <small id="ticketDateForm">
                                <i class="fas fa-clock me-1"></i>{{ now.strftime('%d/%m/%Y %H:%M') }}
                            </small>
                        </div>
                    </div>

                    <div class="ticket-items" id="ticketItemsForm">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                            <p>Aucun article sélectionné</p>
                        </div>
                    </div>

                    <div class="ticket-total">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Sous-total:</span>
                            <span id="subtotalAmountForm">0.00 €</span>
                        </div>
                        <!-- Remise appliquée aux prix individuels -->
                        <div class="d-flex justify-content-between mb-1" id="individualDiscountRowForm" style="display: none;">
                            <span>Remise sur prix:</span>
                            <span id="individualDiscountAmountForm" class="text-success">0.00 €</span>
                        </div>
                        <!-- Remise globale -->
                        <div class="d-flex justify-content-between mb-1" id="globalDiscountRowForm" style="display: none;">
                            <span>Remise <span id="globalDiscountNoteForm"></span>:</span>
                            <span id="globalDiscountAmountForm" class="text-warning">0.00 €</span>
                        </div>
                        <hr class="my-2">
                        <div class="d-flex justify-content-between">
                            <strong>Total à payer:</strong>
                            <strong id="totalAmountForm">0.00 €</strong>
                        </div>

                        <!-- Boutons d'actions -->
                        <div class="mt-2">
                            <button class="btn btn-outline-warning btn-sm w-100 mb-1" id="discountButtonForm"
                                    onclick="StockReplenishmentForm.openDiscountModal()">
                                <i class="fas fa-percent"></i> Appliquer une remise
                            </button>
                            <button class="btn btn-success w-100" id="payButtonForm"
                                    onclick="StockReplenishmentForm.openPaymentModal()" disabled>
                                <i class="fas fa-credit-card"></i> Payer la marchandise
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Raccourcis -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-bolt"></i> Raccourcis</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="loadLastOrder()">
                                <i class="fas fa-history"></i> Dernière commande
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="loadTemplate()">
                                <i class="fas fa-template"></i> Modèle de commande
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="showLowStockItems()">
                                <i class="fas fa-exclamation-triangle"></i> Articles en stock bas
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement -->
<div class="modal fade" id="paymentModalForm" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card"></i> Paiement Fournisseur
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="payment-summary mb-4">
                    <h6>Résumé de la commande</h6>

                    <!-- Liste des articles -->
                    <div class="order-items-summary mb-3" id="paymentOrderItemsForm">
                        <!-- Articles seront ajoutés dynamiquement -->
                    </div>

                    <!-- Totaux -->
                    <div class="border-top pt-2">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Sous-total:</span>
                            <span id="paymentSubtotalForm">0.00 €</span>
                        </div>
                        <!-- Remise appliquée aux prix individuels -->
                        <div class="d-flex justify-content-between mb-1" id="paymentIndividualDiscountRowForm" style="display: none;">
                            <span>Remise sur prix:</span>
                            <span id="paymentIndividualDiscountAmountForm" class="text-success">0.00 €</span>
                        </div>
                        <!-- Remise globale -->
                        <div class="d-flex justify-content-between mb-1" id="paymentGlobalDiscountRowForm" style="display: none;">
                            <span>Remise <span id="paymentGlobalDiscountNoteForm"></span>:</span>
                            <span id="paymentGlobalDiscountAmountForm" class="text-warning">0.00 €</span>
                        </div>
                        <hr class="my-2">
                        <div class="d-flex justify-content-between">
                            <strong>Total à payer:</strong>
                            <strong id="paymentTotalForm" class="text-success">0.00 €</strong>
                        </div>
                    </div>
                </div>

                <div class="payment-methods">
                    <h6>Méthode de paiement</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-primary w-100 payment-method-btn-form"
                                    data-method="cash_caisse">
                                <i class="fas fa-money-bill-wave"></i><br>
                                Cash depuis Caisse
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-info w-100 payment-method-btn-form"
                                    data-method="cheque_compte_banque">
                                <i class="fas fa-money-check"></i><br>
                                Chèque Bancaire
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-success w-100 payment-method-btn-form"
                                    data-method="virement_depuis_compte_banque">
                                <i class="fas fa-exchange-alt"></i><br>
                                Virement Bancaire
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-warning w-100 payment-method-btn-form"
                                    data-method="sortie_cash_banque">
                                <i class="fas fa-university"></i><br>
                                Sortie Cash Banque
                            </button>
                        </div>
                        <div class="col-12 mb-3">
                            <button class="btn btn-outline-secondary w-100 payment-method-btn-form"
                                    data-method="pay_later">
                                <i class="fas fa-clock"></i> Payer plus tard
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Options de traitement -->
                <div class="processing-options mt-4">
                    <h6>Options de traitement</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingTypeForm"
                               id="receiveAndPayForm" value="receive_and_pay" checked>
                        <label class="form-check-label" for="receiveAndPayForm">
                            Recevoir la marchandise et payer maintenant
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingTypeForm"
                               id="receivePayLaterForm" value="receive_only">
                        <label class="form-check-label" for="receivePayLaterForm">
                            Recevoir la marchandise et payer plus tard
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingTypeForm"
                               id="orderOnlyForm" value="order_only">
                        <label class="form-check-label" for="orderOnlyForm">
                            Bon de commande seulement (ne pas mettre à jour le stock)
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Annuler
                </button>
                <button type="button" class="btn btn-success" id="confirmPaymentBtnForm" disabled>
                    <i class="fas fa-check"></i> Confirmer
                </button>
            </div>
        </div>
    </div>
</div>


<!-- Modal de remise -->
<div class="modal fade" id="discountModalForm" tabindex="-1" aria-labelledby="discountModalFormLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="discountModalFormLabel">
                    <i class="fas fa-percent"></i> Appliquer une remise
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Type de remise:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="discountTypeForm" id="discountAmountForm" value="amount" checked>
                        <label class="form-check-label" for="discountAmountForm">
                            Montant fixe (€)
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="discountTypeForm" id="discountPercentageForm" value="percentage">
                        <label class="form-check-label" for="discountPercentageForm">
                            Pourcentage (%)
                        </label>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="discountValueForm" class="form-label">Valeur de la remise:</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="discountValueForm" step="0.01" min="0">
                        <span class="input-group-text" id="discountUnitForm">€</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="applyToItemsForm">
                        <label class="form-check-label" for="applyToItemsForm">
                            Appliquer la remise aux prix d'achat individuels
                        </label>
                        <small class="form-text text-muted">
                            Si coché, la remise sera répartie sur les prix d'achat de chaque article
                        </small>
                    </div>
                </div>
                <div class="alert alert-info">
                    <strong>Sous-total actuel:</strong> <span id="currentSubtotalForm">0.00 €</span><br>
                    <strong>Remise calculée:</strong> <span id="calculatedDiscountForm">0.00 €</span><br>
                    <strong>Nouveau total:</strong> <span id="newTotalForm">0.00 €</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" id="confirmDiscountForm">
                    <i class="fas fa-check"></i> Appliquer la remise
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de modification de prix pour le mode formulaire -->
<div class="modal fade" id="priceEditModalForm" tabindex="-1" aria-labelledby="priceEditModalFormLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="priceEditModalFormLabel">
                    <i class="fas fa-edit"></i> Modifier le prix d'achat
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Article:</label>
                    <div id="editItemNameForm" class="fw-bold"></div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Prix référentiel:</label>
                        <div id="editReferencePriceForm" class="text-muted"></div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Prix actuel:</label>
                        <div id="editCurrentPriceForm" class="text-info"></div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="newPriceForm" class="form-label">Nouveau prix d'achat:</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="newPriceForm" step="0.01" min="0">
                        <span class="input-group-text">€</span>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="priceChangeReasonForm" class="form-label">Raison du changement:</label>
                    <select class="form-select" id="priceChangeReasonForm">
                        <option value="">Sélectionner une raison</option>
                        <option value="market_increase">Augmentation du marché</option>
                        <option value="market_decrease">Baisse du marché</option>
                        <option value="supplier_change">Changement de fournisseur</option>
                        <option value="quality_change">Changement de qualité</option>
                        <option value="promotion">Promotion fournisseur</option>
                        <option value="other">Autre</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmPriceChangeForm">
                    <i class="fas fa-check"></i> Confirmer
                </button>
            </div>
        </div>
    </div>
</div>



<script>
// Variables globales pour le mode formulaire
window.formModeData = {
    currentOrder: [],
    selectedSupplier: null,
    orderTotal: 0
};
</script>
{% endblock %}
