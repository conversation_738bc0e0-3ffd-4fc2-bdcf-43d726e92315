{% extends "base.html" %}

{% block title %}Liste des Commandes d'Achat{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-3">
                <h2 class="mb-0">
                    <i class="fas fa-list"></i> 
                    Liste des Commandes d'Achat
                </h2>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.stock_replenishment_pos_mode') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-cash-register"></i> Mode POS
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_form_mode') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-edit"></i> Mode Formulaire
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-plus"></i> Nouvelle Commande
                    </a>
                    <a href="{{ url_for('inventory.pending_orders') }}" class="btn btn-outline-warning btn-sm me-2">
                        <i class="fas fa-clock"></i> Pending Marchandise
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Filtres -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Fournisseur</label>
                        <select class="form-select" name="supplier_id">
                            <option value="">Tous les fournisseurs</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}" {{ 'selected' if request.args.get('supplier_id') == supplier.id|string }}>
                                {{ supplier.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Statut</label>
                        <select class="form-select" name="status">
                            <option value="">Tous les statuts</option>
                            <option value="pending" {{ 'selected' if request.args.get('status') == 'pending' }}>En attente</option>
                            <option value="partial_received" {{ 'selected' if request.args.get('status') == 'partial_received' }}>Partiellement reçue</option>
                            <option value="received" {{ 'selected' if request.args.get('status') == 'received' }}>Reçue et payée</option>
                            <option value="received_unpaid" {{ 'selected' if request.args.get('status') == 'received_unpaid' }}>Reçue non payée</option>
                            <option value="paid" {{ 'selected' if request.args.get('status') == 'paid' }}>Payée</option>
                            <option value="cancelled" {{ 'selected' if request.args.get('status') == 'cancelled' }}>Annulée</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Date de commande</label>
                        <input type="date" class="form-control" name="order_date" value="{{ request.args.get('order_date', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Actions</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-filter"></i> Filtrer
                            </button>
                            <a href="{{ url_for('inventory.purchase_orders_list') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Effacer
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tableau des commandes -->
        {% if orders.items %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Astuce :</strong> Utilisez la barre de défilement horizontale en bas pour voir toutes les colonnes du tableau.
            </div>

            <div class="table-responsive table-container">
                <table class="table table-hover table-striped orders-table">
                    <thead class="table-dark">
                        <tr>
                            <th scope="col" class="sticky-col">
                                <i class="fas fa-hashtag"></i> Référence
                            </th>
                            <th scope="col">
                                <i class="fas fa-truck"></i> Fournisseur
                            </th>
                            <th scope="col">
                                <i class="fas fa-shopping-cart"></i> Produits Achetés
                            </th>
                            <th scope="col">
                                <i class="fas fa-calendar"></i> Date Commande
                            </th>
                            <th scope="col">
                                <i class="fas fa-calendar-check"></i> Livraison Prévue
                            </th>
                            <th scope="col">
                                <i class="fas fa-calendar-alt"></i> Livraison Effective
                            </th>
                            <th scope="col">
                                <i class="fas fa-euro-sign"></i> Montant Total
                            </th>
                            <th scope="col">
                                <i class="fas fa-boxes"></i> Articles
                            </th>
                            <th scope="col">
                                <i class="fas fa-info-circle"></i> Statut
                            </th>
                            <th scope="col">
                                <i class="fas fa-sticky-note"></i> Notes
                            </th>
                            <th scope="col">
                                <i class="fas fa-percent"></i> Remise
                            </th>
                            <th scope="col">
                                <i class="fas fa-receipt"></i> TVA
                            </th>
                            <th scope="col">
                                <i class="fas fa-clock"></i> Créée le
                            </th>
                            <th scope="col">
                                <i class="fas fa-edit"></i> Modifiée le
                            </th>
                            <th scope="col" class="sticky-actions">
                                <i class="fas fa-cogs"></i> Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders.items %}
                        {% set supplier_colors = ['primary', 'success', 'warning', 'info', 'secondary', 'dark', 'danger'] %}
                        {% set supplier_color = supplier_colors[(order.supplier_id or 0) % supplier_colors|length] %}
                        <tr class="align-middle order-row"
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            data-bs-html="true"
                            title="<strong>{{ order.reference }}</strong><br>
                                   Fournisseur: {{ order.supplier_name }}<br>
                                   Montant: {{ '%.2f'|format(order.total_amount) }} €<br>
                                   Articles: {{ order.items.count() }}<br>
                                   Créée: {{ order.created_at.strftime('%d/%m/%Y') }}<br>
                                   {% if order.notes %}Notes: {{ order.notes[:50] }}{% if order.notes|length > 50 %}...{% endif %}{% endif %}"
                            style="cursor: pointer;">
                            <!-- Référence -->
                            <td class="sticky-col">
                                <strong class="text-primary">{{ order.reference }}</strong>
                            </td>

                            <!-- Fournisseur -->
                            <td>
                                <span class="badge bg-{{ supplier_color }} supplier-badge">
                                    <i class="fas fa-truck me-1"></i>{{ order.supplier_name }}
                                </span>
                            </td>

                            <!-- Produits Achetés -->
                            <td class="products-cell">
                                <div class="products-list">
                                    {% for item in order.items %}
                                        <div class="product-item">
                                            <span class="product-name">{{ item.item_name }}</span>
                                            <span class="product-qty">{{ item.quantity }} {{ item.unit }}</span>
                                        </div>
                                    {% endfor %}
                                </div>
                            </td>

                            <!-- Date de commande -->
                            <td>
                                <span class="date-text">{{ order.order_date.strftime('%d/%m/%Y') }}</span>
                            </td>

                            <!-- Livraison prévue -->
                            <td>
                                {% if order.expected_delivery_date %}
                                    <span class="date-text text-info">{{ order.expected_delivery_date.strftime('%d/%m/%Y') }}</span>
                                    {% if order.expected_delivery_date < now.date() and order.status.value in ['pending', 'partial_received'] %}
                                        <br><span class="badge bg-danger">
                                            <i class="fas fa-exclamation-triangle"></i> En retard
                                        </span>
                                    {% endif %}
                                {% else %}
                                    <span class="no-data">-</span>
                                {% endif %}
                            </td>

                            <!-- Livraison effective -->
                            <td>
                                {% if order.actual_delivery_date %}
                                    <span class="date-text text-success">{{ order.actual_delivery_date.strftime('%d/%m/%Y') }}</span>
                                {% else %}
                                    <span class="no-data">-</span>
                                {% endif %}
                            </td>

                            <!-- Montant total -->
                            <td>
                                <div class="amount-cell">
                                    <strong class="total-amount">{{ "%.2f"|format(order.total_amount) }} €</strong>
                                    {% if order.subtotal != order.total_amount %}
                                        <br><span class="subtotal-amount">HT: {{ "%.2f"|format(order.subtotal) }} €</span>
                                    {% endif %}
                                </div>
                            </td>

                            <!-- Articles -->
                            <td>
                                <span class="badge bg-secondary items-count">
                                    <i class="fas fa-boxes"></i> {{ order.items.count() }}
                                </span>
                            </td>

                            <!-- Statut -->
                            <td>
                                {% if order.status.value == 'pending' %}
                                    <span class="badge status-badge bg-primary">
                                        <i class="fas fa-clock"></i> En attente
                                    </span>
                                {% elif order.status.value == 'partial_received' %}
                                    <span class="badge status-badge bg-warning">
                                        <i class="fas fa-box-open"></i> Partiellement reçue
                                    </span>
                                {% elif order.status.value == 'received' %}
                                    <span class="badge status-badge bg-success">
                                        <i class="fas fa-check"></i> Reçue et payée
                                    </span>
                                {% elif order.status.value == 'received_unpaid' %}
                                    <span class="badge status-badge bg-info">
                                        <i class="fas fa-check-circle"></i> Reçue non payée
                                    </span>
                                {% elif order.status.value == 'paid' %}
                                    <span class="badge status-badge bg-success">
                                        <i class="fas fa-check-double"></i> Reçue et payée
                                    </span>
                                {% elif order.status.value == 'cancelled' %}
                                    <span class="badge status-badge bg-danger">
                                        <i class="fas fa-times"></i> Annulée
                                    </span>
                                {% else %}
                                    <span class="badge status-badge bg-secondary">
                                        <i class="fas fa-question"></i> {{ order.status.value|title }}
                                    </span>
                                {% endif %}
                            </td>

                            <!-- Notes -->
                            <td class="notes-cell">
                                {% if order.notes %}
                                    <span class="notes-text" title="{{ order.notes }}">
                                        {{ order.notes[:30] }}{% if order.notes|length > 30 %}...{% endif %}
                                    </span>
                                {% else %}
                                    <span class="no-data">-</span>
                                {% endif %}
                            </td>

                            <!-- Remise -->
                            <td class="discount-cell">
                                {% if order.discount_amount and order.discount_amount > 0 %}
                                    <div class="discount-info">
                                        {% if order.discount_type == 'percentage' %}
                                            <span class="discount-value">{{ order.discount_amount }}%</span>
                                        {% else %}
                                            <span class="discount-value">{{ "%.2f"|format(order.discount_amount) }} €</span>
                                        {% endif %}
                                        {% if order.apply_discount_to_items %}
                                            <br><span class="discount-type">Sur articles</span>
                                        {% endif %}
                                    </div>
                                {% else %}
                                    <span class="no-data">-</span>
                                {% endif %}
                            </td>

                            <!-- TVA -->
                            <td class="tax-cell">
                                {% if order.tax_rate and order.tax_rate > 0 %}
                                    <div class="tax-info">
                                        <span class="tax-rate">{{ order.tax_rate }}%</span>
                                        <br><span class="tax-amount">{{ "%.2f"|format(order.tax_amount) }} €</span>
                                    </div>
                                {% else %}
                                    <span class="no-data">-</span>
                                {% endif %}
                            </td>

                            <!-- Créée le -->
                            <td class="date-cell">
                                <div class="date-info">
                                    <span class="date-text">{{ order.created_at.strftime('%d/%m/%Y') }}</span>
                                    <br><span class="time-text">{{ order.created_at.strftime('%H:%M') }}</span>
                                </div>
                            </td>

                            <!-- Modifiée le -->
                            <td class="date-cell">
                                {% if order.updated_at != order.created_at %}
                                    <div class="date-info">
                                        <span class="date-text">{{ order.updated_at.strftime('%d/%m/%Y') }}</span>
                                        <br><span class="time-text">{{ order.updated_at.strftime('%H:%M') }}</span>
                                    </div>
                                {% else %}
                                    <span class="no-data">-</span>
                                {% endif %}
                            </td>

                            <!-- Actions -->
                            <td class="sticky-actions">
                                <div class="btn-group-vertical btn-group-sm actions-group" role="group">
                                    <a href="{{ url_for('inventory.purchase_order_details', order_id=order.id) }}"
                                       class="btn btn-outline-info btn-sm mb-1" title="Voir les détails">
                                        <i class="fas fa-eye"></i> Détails
                                    </a>

                                    {% if order.status.value == 'pending' %}
                                        <button class="btn btn-success btn-sm mb-1" onclick="markAsReceived({{ order.id }})" title="Marquer comme reçue">
                                            <i class="fas fa-check"></i> Reçue
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm mb-1" onclick="partialReceive({{ order.id }})" title="Réception partielle">
                                            <i class="fas fa-boxes"></i> Partielle
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm mb-1" onclick="editOrder({{ order.id }})" title="Modifier">
                                            <i class="fas fa-edit"></i> Modifier
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" onclick="cancelOrder({{ order.id }})" title="Annuler">
                                            <i class="fas fa-times"></i> Annuler
                                        </button>
                                    {% elif order.status.value == 'partial_received' %}
                                        <button class="btn btn-success btn-sm mb-1" onclick="markAsReceived({{ order.id }})" title="Finaliser la réception">
                                            <i class="fas fa-check"></i> Finaliser
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" onclick="partialReceive({{ order.id }})" title="Réception partielle">
                                            <i class="fas fa-boxes"></i> Partielle
                                        </button>
                                    {% elif order.status.value == 'received_unpaid' %}
                                        <a href="{{ url_for('inventory.supplier_details', supplier_id=order.supplier_id) }}" class="btn btn-warning btn-sm mb-1" title="Aller payer chez le fournisseur">
                                            <i class="fas fa-credit-card"></i> Payer
                                        </a>
                                        <button class="btn btn-outline-warning btn-sm mb-1" onclick="payPartially({{ order.id }})" title="Payer partiellement">
                                            <i class="fas fa-coins"></i> Partiel
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if orders.pages > 1 %}
            <nav aria-label="Navigation des commandes">
                <ul class="pagination justify-content-center">
                    {% if orders.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.purchase_orders_list', page=orders.prev_num, supplier_id=request.args.get('supplier_id'), status=request.args.get('status'), order_date=request.args.get('order_date')) }}">
                            <i class="fas fa-chevron-left"></i> Précédent
                        </a>
                    </li>
                    {% endif %}

                    {% for page_num in range(1, orders.pages + 1) %}
                        {% if page_num <= 3 or page_num > orders.pages - 3 or (page_num >= orders.page - 1 and page_num <= orders.page + 1) %}
                        <li class="page-item {{ 'active' if page_num == orders.page else '' }}">
                            <a class="page-link" href="{{ url_for('inventory.purchase_orders_list', page=page_num, supplier_id=request.args.get('supplier_id'), status=request.args.get('status'), order_date=request.args.get('order_date')) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% elif page_num == 4 and orders.page > 5 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% elif page_num == orders.pages - 3 and orders.page < orders.pages - 4 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if orders.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.purchase_orders_list', page=orders.next_num, supplier_id=request.args.get('supplier_id'), status=request.args.get('status'), order_date=request.args.get('order_date')) }}">
                            Suivant <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>

            <!-- Informations de pagination -->
            <div class="text-center text-muted mt-3">
                <small>
                    Page {{ orders.page }} sur {{ orders.pages }}
                    ({{ orders.total }} commande{{ 's' if orders.total > 1 else '' }} au total)
                </small>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center text-muted py-5">
                <i class="fas fa-clipboard-list fa-4x mb-3"></i>
                <h4>Aucune commande trouvée</h4>
                <p>Aucune commande ne correspond aux critères de recherche ou vous n'avez pas encore créé de commandes.</p>
                <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Créer une nouvelle commande
                </a>
            </div>
        {% endif %}
    </div>
</div>

<script>
// Fonctions pour les actions sur les commandes
function markAsReceived(orderId) {
    if (confirm('Êtes-vous sûr de vouloir marquer cette commande comme entièrement reçue ?')) {
        fetch(`/inventory/stock-replenishment/orders/${orderId}/receive`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande marquée comme reçue et stock mis à jour');
                location.reload();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    }
}

function partialReceive(orderId) {
    window.location.href = `/inventory/stock-replenishment/pending-orders#order-${orderId}`;
}

function editOrder(orderId) {
    alert('Fonctionnalité de modification en développement');
}

function payOrder(orderId) {
    if (confirm('Êtes-vous sûr de vouloir marquer cette commande comme payée ?')) {
        fetch(`/inventory/stock-replenishment/orders/${orderId}/pay`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande marquée comme payée');
                location.reload();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    }
}

function payPartially(orderId) {
    alert('Fonctionnalité de paiement partiel en développement');
}

function cancelOrder(orderId) {
    if (confirm('Êtes-vous sûr de vouloir annuler cette commande ?')) {
        fetch(`/inventory/stock-replenishment/orders/${orderId}/cancel`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande annulée');
                location.reload();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    }
}

// Initialiser les tooltips Bootstrap
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
