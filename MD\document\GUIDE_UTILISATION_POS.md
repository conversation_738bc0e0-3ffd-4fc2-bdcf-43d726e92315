# 📖 Guide d'Utilisation - Système POS

## 🚀 **DÉMARRAGE RAPIDE**

### **1. Connexion au système**
- Ouvrez votre navigateur
- Allez sur votre URL POS
- Connectez-vous avec vos identifiants
- Vous arrivez sur le tableau de bord

### **2. Ouverture de caisse (obligatoire)**
- Allez dans **Caisse** > **Gestion de caisse**
- Cliquez sur **"Ouvrir la caisse"**
- Saisissez le **fond de caisse** initial
- Validez l'ouverture

---

## 🛒 **PRENDRE UNE COMMANDE**

### **Étape 1 : Accéder au POS**
- Cliquez sur **"Point de Vente"** dans le menu
- Ou utilisez le raccourci **POS** du tableau de bord

### **Étape 2 : Nouvelle commande**
- Cliquez sur **"Nouvelle commande"** (bouton vert)
- Choisissez le **type de service** :
  - **Sur place** : Client mange sur place
  - **À emporter** : Client emporte sa commande
  - **Livraison** : Commande livrée
  - **Drive** : Service au volant

### **Étape 3 : Sélection de table (si sur place)**
- Choisissez la **salle**
- Sélectionnez une **table libre** (verte)
- Indiquez le **nombre de couverts** (1-8+)
- Validez la sélection

### **Étape 4 : Ajouter des produits**
- **Cliquez** sur les produits pour les ajouter
- **Filtrez** par catégorie si nécessaire
- **Recherchez** un produit spécifique
- **Modifiez les quantités** dans le panier
- **Supprimez** des articles si besoin

### **Étape 5 : Finaliser la commande**
- **Option A - Envoi en cuisine** :
  - Ajoutez une **note cuisine** si nécessaire
  - Cliquez **"Envoyer en cuisine"**
  - La commande passe en préparation
  
- **Option B - Paiement direct** :
  - Cliquez **"Paiement"**
  - Choisissez la **méthode de paiement**
  - Validez le montant

---

## 💳 **TRAITER UN PAIEMENT**

### **Méthodes disponibles :**
- **Espèces** : Paiement en liquide
- **Carte bancaire** : CB, Visa, Mastercard
- **Chèque** : Paiement par chèque
- **Virement** : Virement bancaire

### **Processus de paiement :**
1. **Sélectionnez** la méthode
2. **Saisissez** le montant (si différent)
3. **Validez** le paiement
4. **Imprimez** le ticket client
5. **Libération automatique** de la table

### **Paiements partiels :**
- Possibilité de **payer en plusieurs fois**
- Le système **calcule automatiquement** le restant dû
- **Historique** des paiements conservé

---

## 🍽️ **GESTION DES TABLES**

### **Statuts des tables :**
- **🟢 Libre** : Table disponible
- **🔴 Occupée** : Table avec clients
- **🟡 Réservée** : Table réservée
- **⚫ Hors service** : Table indisponible

### **Actions sur les tables :**
- **Occuper** : Assigner une commande
- **Libérer** : Remettre disponible
- **Réserver** : Bloquer pour réservation
- **Mettre hors service** : Maintenance

### **Plan de salle :**
- **Vue interactive** des tables
- **Glisser-déposer** pour repositionner
- **Zoom** et navigation
- **Informations** en temps réel

---

## 📋 **GESTION DES COMMANDES**

### **Suivi des commandes :**
- **Liste des ventes** : Toutes les commandes
- **Filtres** par statut, date, serveur
- **Détails** de chaque commande
- **Historique** complet

### **Statuts des commandes :**
- **En attente** : Pas encore payée
- **Payée** : Paiement effectué
- **En cuisine** : En préparation
- **Prête** : Prête à servir
- **Livrée** : Servie au client
- **Terminée** : Commande finalisée

### **Commandes en attente :**
- **Mettre en attente** : Sauvegarder temporairement
- **Reprendre** : Continuer une commande
- **Supprimer** : Annuler définitivement

---

## 📊 **CONSULTER LES RAPPORTS**

### **Tableau de bord :**
- **Chiffre d'affaires** du jour
- **Nombre de ventes**
- **Panier moyen**
- **Graphiques** en temps réel

### **Rapports détaillés :**
- **Ventes par période** (jour, semaine, mois)
- **Performance par produit**
- **Analyse par serveur**
- **Heures de pointe**

### **Exports :**
- **PDF** pour impression
- **Excel** pour analyse
- **Envoi par email**

---

## 🏪 **GESTION DE L'INVENTAIRE**

### **Ajouter un produit :**
1. **Inventaire** > **Produits**
2. **"Nouveau produit"**
3. Remplissez les **informations** :
   - Nom et description
   - Prix de vente
   - Catégorie
   - Image (optionnel)
4. **Sauvegardez**

### **Gestion des stocks :**
- **Quantités** en temps réel
- **Alertes** de stock faible
- **Mouvements** de stock
- **Inventaires** périodiques

### **Catégories :**
- **Organisez** vos produits
- **Filtrage** rapide au POS
- **Personnalisation** des couleurs

---

## 👥 **GESTION DES CLIENTS**

### **Fiche client :**
- **Informations** personnelles
- **Historique** des commandes
- **Préférences** alimentaires
- **Adresses** de livraison

### **Programme de fidélité :**
- **Points** automatiques
- **Récompenses** personnalisées
- **Suivi** de la fidélité

---

## ⚙️ **PARAMÈTRES**

### **Configuration restaurant :**
- **Nom** et adresse
- **Horaires** d'ouverture
- **Contact** et réseaux sociaux
- **Logo** et branding

### **Paramètres POS :**
- **Taux de TVA**
- **Méthodes de paiement**
- **Imprimantes**
- **Notifications**

### **Utilisateurs :**
- **Ajouter** des employés
- **Définir** les rôles
- **Gérer** les permissions
- **Historique** des connexions

---

## 🔧 **MAINTENANCE QUOTIDIENNE**

### **Début de journée :**
1. ✅ **Ouvrir la caisse**
2. ✅ **Vérifier les stocks**
3. ✅ **Activer les tables**
4. ✅ **Tester les imprimantes**

### **Pendant le service :**
- 🔄 **Surveiller** les commandes
- 📊 **Consulter** les statistiques
- 🍽️ **Gérer** les tables
- 💰 **Traiter** les paiements

### **Fin de journée :**
1. ✅ **Fermer la caisse**
2. ✅ **Vérifier** les commandes
3. ✅ **Générer** les rapports
4. ✅ **Sauvegarder** les données

---

## 🆘 **RÉSOLUTION DE PROBLÈMES**

### **Problèmes fréquents :**

#### **"Stock insuffisant"**
- **Cause** : Quantité en stock épuisée
- **Solution** : Réapprovisionner ou désactiver le produit

#### **"Table déjà occupée"**
- **Cause** : Table non libérée après paiement
- **Solution** : Libérer manuellement la table

#### **"Erreur de paiement"**
- **Cause** : Problème de caisse ou réseau
- **Solution** : Vérifier la caisse et réessayer

#### **"Impression impossible"**
- **Cause** : Imprimante déconnectée
- **Solution** : Vérifier la connexion et le papier

### **Support technique :**
- 📧 **Email** : <EMAIL>
- 📞 **Téléphone** : 01 23 45 67 89
- 💬 **Chat** : Assistant IA intégré
- 🕐 **Horaires** : 9h-18h du lundi au vendredi

---

## 📱 **CONSEILS D'UTILISATION**

### **Optimisation :**
- Utilisez les **raccourcis clavier**
- **Organisez** vos catégories logiquement
- **Formez** votre équipe régulièrement
- **Sauvegardez** vos données

### **Bonnes pratiques :**
- **Vérifiez** les commandes avant envoi
- **Confirmez** les paiements
- **Nettoyez** l'écran régulièrement
- **Mettez à jour** les prix

### **Sécurité :**
- **Déconnectez-vous** après usage
- **Changez** les mots de passe régulièrement
- **Limitez** les accès selon les rôles
- **Surveillez** les transactions
