- Ajoutez en optionnelLe le choix ou cliquer Sur un client Si non alors client anonyme pour les ventes depuis POS, pour les ventes depuis le site en ligne alors customer user doit etre affiché dans les pages des ventes ou il y'a le champ pour cela.

- New_features.md
- indexation voir discution chatgpt
- connexion et deconnexion rapide avec changement d'utilisateurs et acces direct page ventes et avec icone changement d'utilisateur pour connexion simplifiée
- stockage des images menus et produits...etc dans cloud apres deployment soit avec GCP ou neon ou compte gratuit et api cloudflare R2 Egness gratuit qui est le telechargement des images lors de raffraichissement des pages (comparer les prix et reduire la taille des images)

- mode off-line (s'il n'y a pas de connexion internet) pour les ventes depuis POS

- FIFO Au coût moyen des ingrédients pour prix d'achat
- Indicateurs financiers pour la valeur de l'entreprise selon les chiffres d'affaires et les bénéfices
- transformer un ticket de caisse en facture ou directement créer mode facture à selectionner (mode caisse ou mode facture)
- la liste deroulante des rapports doit etre dans une page index.html à creer et dans cette page index.html un bouton pour ouvrir un formulaire de  personnalisation des rapports avec boutons radio

- ne pas montrer le bouton livré que si le paiement était PAYE en ligne et reste que la livraison a cliquer dessus

- les montant des paiements en ligne doivent s'ajouter au CA et caisse etc avec methode paiement en ligne

- réviser ce qui manque dans les rapports pour l'affichage, les filtres des ventes par table et room et par ventes en ligne et payés en ligne ou depuis POS

ventes en ligne et payés en ligne

1. ? Voir propositopn chatgpt pour avoir deux champs un pour livré et un pour payé et pour la possibilité payé en ligne et non encore livré les montants des commandes en ligne ne s'ajoutent pas à la caisse avec methode livré ni je peux les faire payer depuis la page des ventes 

2. les boutons d'actions depuis cette page : http://127.0.0.1:5000/admin/orders ne sont pas encore fonctionneles

3. les produits vendus en lignes ne sont pas reduit du stock que se soient avec recette ou sans recette

4. l'historique des commandes ne s'affichent pas au client depuis le site de commandes en ligne, et s'il a par exemple deux ou trois commandes validés il n y a que le dernier qui s'affiche et ne peux pas encore suivre les autres commandes

5. notifications javascript vs soketio deja installé et voir repose chatgpt.

- fonction budjet n'est pas encore implementé dans expenses/views.py, ni son template.html, ni son lien (dans urls.py)

- Products variants n'est pas encore implementé AVEC OWNER_ID dans inventory/models_product.py, ni dans son template.html, ni dans son formulaire (forms.py)

# owner_id missing dans:

- [ ] app\modules\inventory\models_product.py      class ProductVariant(db.Model):
- [ ] app\modules\inventory\models_recipe.py              ALL class
- [ ] app\modules\inventory\models_product_variant.py     class variant
- [ ] app\modules\tables\models.py     class Table et reservations et routes

- donc une revision globale module par module est nécessaire


# a verifier le doublement des forms(ingredients, product et recipe) et qui existent dans inventory/forms.py

# route supplier manque  def supplier_categories et mise à jour de son html

# Q? owner_id dans pos medels et routes et forms

# dans la route settings/views.py il y a 3 routes (system(pou le backup), alerts) soit il n'ont pas leurs templates.html et leurs lien ou en plus correction du code
et aussi settings/general.html et settings/sale.html n'ont pas leurs fonctions dans views.py

# rassembler ou creer un dropdown pour choisir le rapport dans une page index.html à creer et dans cette page index.html un bouton pour ouvrir un formulaire de  personnalisation des rapports avec boutons radio


# demander à l'agent l'optimisation et revision du code module par module et Q? où une fonction ou fichier ou autres est utilisé ou si il est encore nécessaire ou pas utilisé pour l'utiliser quelques parts

- reste encore à voir ce qui est utile ou non dans les rapports (voir les pages papports)

- la page reports/receipt.html n'a aucune fonction dans reports/views.py

- templates/orders (sert au commandes qui sernt automatisés pour les fournissers)

- task 1: 
       apres avoir Marqué une commande comme servie Et vouloir voir la commande depuis Liste des ventes puis 
       en cliquant sur paiement alors la quantité du produit ne se réduit pas, vérifier aussi si la même chose 
       pour produit avec recettes

       et correction de quantité discount de produit avec recette ou fiche technique

       CA dansrapport et caisse etc pour commande servi et autres

tasks 2:
       Agent AI service technique ou service client
       Agent AI conseiller et selon les rapports de ventes et fiche technique produit et gestion des employés et charges ...

ajouter un rappor de ventes ou ajouter filtre des ventes des salles et tables selon CA ou autres (Ou bien les statistiques des ventes par salle et par table Et avec chiffre d'affaires du plus haut au plus bas et aussi par nombre de clientset par nombre d'articles etc...)