{% extends "base.html" %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/suppliers.css') }}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0">{{ supplier.name }}</h2>
                        <div>
                            <a href="{{ url_for('inventory.edit_supplier', id=supplier.id) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Modifier
                            </a>
                            <a href="{{ url_for('inventory.suppliers') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Informations Générales</h5>
                            <table class="table">
                                <tr>
                                    <th>Nom</th>
                                    <td>{{ supplier.name }}</td>
                                </tr>
                                <tr>
                                    <th>Catégorie</th>
                                    <td>
                                        {% if supplier.category %}
                                            <span class="badge" style="background-color: {{ supplier.category.color }};">
                                                <i class="{{ supplier.category.icon }}"></i> {{ supplier.category.name }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">Sans catégorie</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Note</th>
                                    <td>
                                        {% if supplier.rating > 0 %}
                                            <span class="text-warning">{{ supplier.display_rating }}</span>
                                        {% else %}
                                            <span class="text-muted">Non noté</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Statut</th>
                                    <td>
                                        {% if supplier.is_active %}
                                            <span class="badge bg-success">Actif</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactif</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Informations de Contact</h5>
                            <table class="table">
                                <tr>
                                    <th>Contact</th>
                                    <td>{{ supplier.contact_name or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>
                                        {% if supplier.email %}
                                            <a href="mailto:{{ supplier.email }}">{{ supplier.email }}</a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Téléphone</th>
                                    <td>
                                        {% if supplier.phone %}
                                            <a href="tel:{{ supplier.phone }}">{{ supplier.phone }}</a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Site web</th>
                                    <td>
                                        {% if supplier.website %}
                                            <a href="{{ supplier.website }}" target="_blank">
                                                {{ supplier.website }} <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Informations Commerciales</h5>
                            <table class="table">
                                <tr>
                                    <th>N° TVA/SIRET</th>
                                    <td>{{ supplier.tax_id or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Conditions de paiement</th>
                                    <td>{{ supplier.payment_terms or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Adresse</h5>
                            {% if supplier.address %}
                                <div class="border p-3 rounded">
                                    {{ supplier.address|nl2br }}
                                </div>
                            {% else %}
                                <p class="text-muted">Aucune adresse renseignée</p>
                            {% endif %}
                        </div>
                    </div>

                    {% if supplier.notes %}
                    <div class="row">
                        <div class="col-12">
                            <h5>Notes</h5>
                            <div class="alert alert-info">
                                {{ supplier.notes|nl2br }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer text-muted">
                    <small>
                        Créé le {{ supplier.created_at.strftime('%d/%m/%Y à %H:%M') }}
                        {% if supplier.updated_at != supplier.created_at %}
                            • Modifié le {{ supplier.updated_at.strftime('%d/%m/%Y à %H:%M') }}
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Actions rapides -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Actions Rapides</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('inventory.edit_supplier', id=supplier.id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Modifier les informations
                        </a>
                        {% if supplier.email %}
                        <a href="mailto:{{ supplier.email }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope"></i> Envoyer un email
                        </a>
                        {% endif %}
                        {% if supplier.phone %}
                        <a href="tel:{{ supplier.phone }}" class="btn btn-outline-success">
                            <i class="fas fa-phone"></i> Appeler
                        </a>
                        {% endif %}
                        {% if supplier.website %}
                        <a href="{{ supplier.website }}" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-globe"></i> Visiter le site web
                        </a>
                        {% endif %}
                        <a href="{{ url_for('inventory.supplier_contacts', supplier_id=supplier.id) }}"
                           class="btn btn-outline-secondary">
                            <i class="fas fa-history"></i> Historique des contacts
                        </a>
                        <a href="{{ url_for('inventory.add_supplier_contact', supplier_id=supplier.id) }}"
                           class="btn btn-success">
                            <i class="fas fa-plus"></i> Ajouter un contact
                        </a>
                        <a href="{{ url_for('inventory.pending_supplier_contacts') }}"
                           class="btn btn-outline-warning">
                            <i class="fas fa-clock"></i> Contacts en attente
                        </a>
                        <a href="{{ url_for('inventory.stock_replenishment_pos_mode') }}?supplier_id={{ supplier.id }}"
                           class="btn btn-primary">
                            <i class="fas fa-truck-loading"></i> Nouvelle commande
                        </a>
                    </div>
                </div>
            </div>

            <!-- Factures en attente -->
            {% if pending_invoices %}
            <div class="card mb-4">
                <div class="card-header bg-warning">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        Factures en Attente de Paiement
                    </h5>
                </div>
                <div class="card-body">
                    {% for invoice in pending_invoices %}
                    <div class="border rounded p-3 mb-3 invoice-card {{ 'border-danger' if invoice.is_overdue else 'border-warning' }}"
                         data-bs-toggle="tooltip"
                         data-bs-placement="left"
                         data-bs-html="true"
                         title="<div class='invoice-tooltip'>
                                    <div class='tooltip-header'>
                                        <strong><i class='fas fa-file-invoice'></i> {{ invoice.reference }}</strong>
                                        {% if invoice.is_overdue %}
                                            <span class='badge bg-danger ms-2'>En retard</span>
                                        {% endif %}
                                    </div>
                                    <div class='tooltip-content'>
                                        <div class='tooltip-row'>
                                            <i class='fas fa-calendar-plus text-info'></i>
                                            <span>Créée le: {{ invoice.created_at.strftime('%d/%m/%Y à %H:%M') if invoice.created_at else 'N/A' }}</span>
                                        </div>
                                        {% if invoice.due_date %}
                                        <div class='tooltip-row'>
                                            <i class='fas fa-calendar-check text-warning'></i>
                                            <span>Échéance: {{ invoice.due_date.strftime('%d/%m/%Y') }}</span>
                                        </div>
                                        {% if invoice.is_overdue %}
                                        <div class='tooltip-row'>
                                            <i class='fas fa-exclamation-triangle text-danger'></i>
                                            <span>Retard: {{ invoice.days_overdue }} jour(s)</span>
                                        </div>
                                        {% endif %}
                                        {% endif %}
                                        <div class='tooltip-row'>
                                            <i class='fas fa-euro-sign text-success'></i>
                                            <span>Montant à payer: {{ '%.2f'|format(invoice.remaining_amount) }} €</span>
                                        </div>
                                        {% if invoice.original_amount and invoice.original_amount != invoice.remaining_amount %}
                                        <div class='tooltip-row'>
                                            <i class='fas fa-receipt text-secondary'></i>
                                            <span>Montant initial: {{ '%.2f'|format(invoice.original_amount) }} €</span>
                                        </div>
                                        <div class='tooltip-row'>
                                            <i class='fas fa-check-circle text-success'></i>
                                            <span>Déjà payé: {{ '%.2f'|format(invoice.original_amount - invoice.remaining_amount) }} €</span>
                                        </div>
                                        {% endif %}
                                        {% if invoice.purchase_order %}
                                        <div class='tooltip-row'>
                                            <i class='fas fa-shopping-cart text-primary'></i>
                                            <span>Commande: {{ invoice.purchase_order.reference }}</span>
                                        </div>
                                        {% endif %}
                                        {% if invoice.notes %}
                                        <div class='tooltip-row'>
                                            <i class='fas fa-sticky-note text-info'></i>
                                            <span>Notes: {{ invoice.notes[:80] }}{% if invoice.notes|length > 80 %}...{% endif %}</span>
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class='tooltip-footer'>
                                        <small><i class='fas fa-info-circle'></i> Survolez pour voir tous les détails</small>
                                    </div>
                                </div>">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-file-invoice text-primary"></i>
                                    Facture {{ invoice.reference }}
                                    {% if invoice.is_overdue %}
                                        <span class="badge bg-danger">En retard</span>
                                    {% endif %}
                                </h6>
                                <p class="mb-1">
                                    <strong><i class="fas fa-euro-sign text-success"></i> Montant:</strong> {{ "%.2f"|format(invoice.remaining_amount) }} €
                                </p>
                                {% if invoice.due_date %}
                                <p class="mb-1">
                                    <strong><i class="fas fa-calendar-check text-warning"></i> Échéance:</strong> {{ invoice.due_date.strftime('%d/%m/%Y') }}
                                    {% if invoice.is_overdue %}
                                        <span class="text-danger">({{ invoice.days_overdue }} jours de retard)</span>
                                    {% endif %}
                                </p>
                                {% endif %}
                                {% if invoice.purchase_order %}
                                <small class="text-muted">
                                    <i class="fas fa-shopping-cart"></i> Commande: {{ invoice.purchase_order.reference }}
                                </small>
                                {% endif %}
                            </div>
                            <div class="text-end">
                                <button class="btn btn-sm btn-success"
                                        onclick="payInvoice({{ invoice.id }})">
                                    <i class="fas fa-credit-card"></i> Payer
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}

                    {% if pending_invoices|length > 1 %}
                    <div class="text-center mt-3">
                        <button class="btn btn-primary" onclick="payAllInvoices({{ supplier.id }})">
                            <i class="fas fa-credit-card"></i>
                            Payer toutes les factures ({{ "%.2f"|format(total_pending_amount) }} €)
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Statistiques -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Statistiques</h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h3 class="text-primary">{{ purchase_orders_count or 0 }}</h3>
                            <p class="mb-0">Commandes passées</p>
                        </div>
                        <div class="mb-3">
                            <h3 class="text-success">{{ "%.2f"|format(total_purchases or 0) }} €</h3>
                            <p class="mb-0">Total des achats</p>
                        </div>
                        <div class="mb-3">
                            <h3 class="text-warning">{{ "%.2f"|format(total_pending_amount or 0) }} €</h3>
                            <p class="mb-0">Factures en attente</p>
                        </div>
                        <div>
                            <h3 class="text-info">{{ products_supplied_count or 0 }}</h3>
                            <p class="mb-0">Produits fournis</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement d'une facture -->
<div class="modal fade" id="payInvoiceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card"></i> Payer la Facture
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="invoiceDetails">
                    <!-- Détails de la facture seront chargés ici -->
                </div>

                <div class="mt-3">
                    <h6>Méthode de paiement</h6>
                    <div class="row">
                        <div class="col-6 mb-2">
                            <button class="btn btn-outline-primary w-100 payment-method-btn"
                                    data-method="cash_caisse">
                                <i class="fas fa-money-bill-wave"></i><br>
                                Cash Caisse
                            </button>
                        </div>
                        <div class="col-6 mb-2">
                            <button class="btn btn-outline-info w-100 payment-method-btn"
                                    data-method="cheque_compte_banque">
                                <i class="fas fa-money-check"></i><br>
                                Chèque Bancaire
                            </button>
                        </div>
                        <div class="col-6 mb-2">
                            <button class="btn btn-outline-success w-100 payment-method-btn"
                                    data-method="virement_depuis_compte_banque">
                                <i class="fas fa-exchange-alt"></i><br>
                                Virement
                            </button>
                        </div>
                        <div class="col-6 mb-2">
                            <button class="btn btn-outline-warning w-100 payment-method-btn"
                                    data-method="sortie_cash_banque">
                                <i class="fas fa-university"></i><br>
                                Cash Banque
                            </button>
                        </div>
                    </div>
                </div>

                <div class="mt-3" id="bankAccountSection" style="display: none;">
                    <label class="form-label">Compte bancaire</label>
                    <select class="form-select" id="bankAccountSelect">
                        <option value="">Sélectionner un compte</option>
                        <!-- Options seront chargées dynamiquement -->
                    </select>
                </div>

                <div class="mt-3">
                    <label class="form-label">Référence du paiement (optionnel)</label>
                    <input type="text" class="form-control" id="paymentReference"
                           placeholder="Numéro de chèque, référence virement...">
                </div>

                <div class="mt-3">
                    <label class="form-label">Notes</label>
                    <textarea class="form-control" id="paymentNotes" rows="2"
                              placeholder="Notes sur le paiement..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Annuler
                </button>
                <button type="button" class="btn btn-success" id="confirmPaymentBtn" disabled>
                    <i class="fas fa-check"></i> Confirmer le paiement
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement groupé -->
<div class="modal fade" id="payAllInvoicesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card"></i> Payer Toutes les Factures
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="allInvoicesDetails">
                    <!-- Liste des factures sera chargée ici -->
                </div>

                <!-- Même section de méthodes de paiement que pour une facture -->
                <div class="mt-3">
                    <h6>Méthode de paiement</h6>
                    <div class="row">
                        <div class="col-6 mb-2">
                            <button class="btn btn-outline-primary w-100 payment-method-btn-all"
                                    data-method="cash_caisse">
                                <i class="fas fa-money-bill-wave"></i><br>
                                Cash Caisse
                            </button>
                        </div>
                        <div class="col-6 mb-2">
                            <button class="btn btn-outline-info w-100 payment-method-btn-all"
                                    data-method="cheque_compte_banque">
                                <i class="fas fa-money-check"></i><br>
                                Chèque Bancaire
                            </button>
                        </div>
                        <div class="col-6 mb-2">
                            <button class="btn btn-outline-success w-100 payment-method-btn-all"
                                    data-method="virement_depuis_compte_banque">
                                <i class="fas fa-exchange-alt"></i><br>
                                Virement
                            </button>
                        </div>
                        <div class="col-6 mb-2">
                            <button class="btn btn-outline-warning w-100 payment-method-btn-all"
                                    data-method="sortie_cash_banque">
                                <i class="fas fa-university"></i><br>
                                Cash Banque
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Annuler
                </button>
                <button type="button" class="btn btn-success" id="confirmAllPaymentBtn" disabled>
                    <i class="fas fa-check"></i> Confirmer tous les paiements
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let selectedPaymentMethod = null;
let selectedInvoiceId = null;
let selectedSupplierId = {{ supplier.id }};

// Fonctions de paiement
function payInvoice(invoiceId) {
    selectedInvoiceId = invoiceId;

    // Charger les détails de la facture
    fetch(`/inventory/api/supplier-invoice/${invoiceId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('invoiceDetails').innerHTML = `
                <div class="alert alert-info">
                    <h6>Facture ${data.reference}</h6>
                    <p><strong>Montant à payer:</strong> ${data.remaining_amount.toFixed(2)} €</p>
                    ${data.due_date ? `<p><strong>Échéance:</strong> ${new Date(data.due_date).toLocaleDateString('fr-FR')}</p>` : ''}
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('payInvoiceModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du chargement des détails de la facture');
        });
}

function payAllInvoices(supplierId) {
    // Charger toutes les factures en attente
    fetch(`/inventory/api/supplier-invoices/pending/${supplierId}`)
        .then(response => response.json())
        .then(data => {
            const totalAmount = data.reduce((sum, invoice) => sum + invoice.remaining_amount, 0);

            document.getElementById('allInvoicesDetails').innerHTML = `
                <div class="alert alert-warning">
                    <h6>${data.length} facture(s) en attente</h6>
                    <p><strong>Montant total:</strong> ${totalAmount.toFixed(2)} €</p>
                </div>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Référence</th>
                                <th>Montant</th>
                                <th>Échéance</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.map(invoice => `
                                <tr>
                                    <td>${invoice.reference}</td>
                                    <td>${invoice.remaining_amount.toFixed(2)} €</td>
                                    <td>${invoice.due_date ? new Date(invoice.due_date).toLocaleDateString('fr-FR') : '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('payAllInvoicesModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du chargement des factures');
        });
}

// Gestion des méthodes de paiement
document.querySelectorAll('.payment-method-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        // Réinitialiser les autres boutons
        document.querySelectorAll('.payment-method-btn').forEach(b => {
            b.classList.remove('btn-primary');
            b.classList.add('btn-outline-primary');
        });

        // Activer le bouton sélectionné
        this.classList.remove('btn-outline-primary');
        this.classList.add('btn-primary');

        selectedPaymentMethod = this.dataset.method;

        // Afficher/masquer la section compte bancaire
        const bankSection = document.getElementById('bankAccountSection');
        if (['cheque_compte_banque', 'virement_depuis_compte_banque', 'sortie_cash_banque'].includes(selectedPaymentMethod)) {
            bankSection.style.display = 'block';
            loadBankAccounts();
        } else {
            bankSection.style.display = 'none';
        }

        document.getElementById('confirmPaymentBtn').disabled = false;
    });
});

// Même logique pour les paiements groupés
document.querySelectorAll('.payment-method-btn-all').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.payment-method-btn-all').forEach(b => {
            b.classList.remove('btn-primary');
            b.classList.add('btn-outline-primary');
        });

        this.classList.remove('btn-outline-primary');
        this.classList.add('btn-primary');

        selectedPaymentMethod = this.dataset.method;
        document.getElementById('confirmAllPaymentBtn').disabled = false;
    });
});

function loadBankAccounts() {
    fetch('/inventory/api/bank-accounts')
        .then(response => response.json())
        .then(accounts => {
            const select = document.getElementById('bankAccountSelect');
            select.innerHTML = '<option value="">Sélectionner un compte</option>' +
                accounts.map(account =>
                    `<option value="${account.id}">${account.name} (${account.balance.toFixed(2)} €)</option>`
                ).join('');
        })
        .catch(error => console.error('Erreur lors du chargement des comptes:', error));
}

// Confirmation des paiements
document.getElementById('confirmPaymentBtn').addEventListener('click', function() {
    if (!selectedPaymentMethod) {
        alert('Veuillez sélectionner une méthode de paiement');
        return;
    }

    const paymentData = {
        invoice_id: selectedInvoiceId,
        payment_method: selectedPaymentMethod,
        bank_account_id: document.getElementById('bankAccountSelect').value || null,
        reference: document.getElementById('paymentReference').value,
        notes: document.getElementById('paymentNotes').value
    };

    fetch('/inventory/api/pay-supplier-invoice', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify(paymentData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Paiement effectué avec succès');
            location.reload();
        } else {
            alert('Erreur: ' + (data.error || 'Erreur inconnue'));
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur de connexion');
    });
});

// Initialiser les tooltips Bootstrap pour les factures
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            html: true,
            placement: 'left',
            trigger: 'hover focus',
            delay: { show: 300, hide: 100 }
        });
    });
});
</script>
{% endblock %}
