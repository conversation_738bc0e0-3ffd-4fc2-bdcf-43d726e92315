{% extends "base.html" %}

{% block title %}Détails Commande {{ order.reference }}{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-file-alt"></i> Commande {{ order.reference }}
                </h4>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.stock_replenishment_pos_mode') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-cash-register"></i> Mode POS
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_form_mode') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-edit"></i> Mode Formulaire
                    </a>
                    <a href="{{ url_for('inventory.purchase_orders_list') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Informations de la commande -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6><i class="fas fa-info-circle"></i> Informations de la Commande</h6>
                        {% if order.status.value == 'pending' %}
                            <span class="badge bg-primary">
                                <i class="fas fa-clock"></i> En attente
                            </span>
                        {% elif order.status.value == 'partial_received' %}
                            <span class="badge bg-warning">
                                <i class="fas fa-box-open"></i> Partiellement reçue
                            </span>
                        {% elif order.status.value == 'received' %}
                            <span class="badge bg-success">
                                <i class="fas fa-check"></i> Reçue et payée
                            </span>
                        {% elif order.status.value == 'received_unpaid' %}
                            <span class="badge bg-info">
                                <i class="fas fa-check-circle"></i> Reçue non payée
                            </span>
                        {% elif order.status.value == 'paid' %}
                            <span class="badge bg-success">
                                <i class="fas fa-check-double"></i> Payée
                            </span>
                        {% elif order.status.value == 'cancelled' %}
                            <span class="badge bg-danger">
                                <i class="fas fa-times"></i> Annulée
                            </span>
                        {% else %}
                            <span class="badge bg-secondary">
                                <i class="fas fa-question"></i> {{ order.status.value|title }}
                            </span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Référence:</strong><br>
                                {{ order.reference }}
                            </div>
                            <div class="col-md-6">
                                <strong>Fournisseur:</strong><br>
                                <a href="{{ url_for('inventory.supplier_details', id=order.supplier_id) }}" class="text-primary">
                                    {{ order.supplier_name }}
                                </a>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Date de commande:</strong><br>
                                {{ order.order_date.strftime('%d/%m/%Y') }}
                            </div>
                            <div class="col-md-6">
                                <strong>Montant total:</strong><br>
                                <span class="h6 text-success">{{ "%.2f"|format(order.total_amount) }} €</span>
                            </div>
                        </div>

                        <!-- Informations sur les remises -->
                        {% set total_item_discounts = 0 %}
                        {% set has_items_with_original_price = false %}
                        {% for item in order.items %}
                            {% if item.discount_amount %}
                                {% set total_item_discounts = total_item_discounts + (item.discount_amount * item.quantity) %}
                            {% endif %}
                            {% if item.original_unit_price and item.original_unit_price != item.unit_price %}
                                {% set has_items_with_original_price = true %}
                            {% endif %}
                        {% endfor %}

                        {% set has_discount = (order.discount_amount and order.discount_amount > 0) or (order.apply_discount_to_items and has_items_with_original_price) %}



                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Remise appliquée:</strong><br>
                                {% if has_discount %}
                                    {% if order.apply_discount_to_items and has_items_with_original_price %}
                                        {% if total_item_discounts > 0 %}
                                            <span class="text-warning">{{ "%.2f"|format(total_item_discounts) }} €</span>
                                        {% else %}
                                            <!-- Calculer la remise à partir de la différence des prix -->
                                            {% set calculated_discount = 0 %}
                                            {% for item in order.items %}
                                                {% if item.original_unit_price and item.original_unit_price != item.unit_price %}
                                                    {% set calculated_discount = calculated_discount + ((item.original_unit_price - item.unit_price) * item.quantity) %}
                                                {% endif %}
                                            {% endfor %}
                                            <span class="text-warning">{{ "%.2f"|format(calculated_discount) }} €</span>
                                        {% endif %}
                                        {% if order.discount_type == 'percentage' and order.discount_amount %}
                                            <small class="text-muted d-block">
                                                ({{ "%.2f"|format(order.discount_amount) }}% répartie sur les articles)
                                            </small>
                                        {% else %}
                                            <small class="text-muted d-block">
                                                (répartie sur les prix individuels)
                                            </small>
                                        {% endif %}
                                    {% elif order.discount_amount and order.discount_amount > 0 %}
                                        {% if order.discount_type == 'percentage' %}
                                            <span class="text-warning">{{ "%.2f"|format(order.discount_amount) }}%</span>
                                            <small class="text-muted d-block">
                                                ({{ "%.2f"|format(order.subtotal * order.discount_amount / 100) }} €)
                                            </small>
                                        {% else %}
                                            <span class="text-warning">{{ "%.2f"|format(order.discount_amount) }} €</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">Aucune remise (0 €)</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">Aucune remise (0 €)</span>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <strong>Application de la remise:</strong><br>
                                {% if order.apply_discount_to_items %}
                                    <span class="text-info">
                                        <i class="fas fa-tag"></i> Appliquée sur les prix individuels
                                    </span>
                                {% else %}
                                    <span class="text-info">
                                        <i class="fas fa-calculator"></i> Appliquée sur le total seulement
                                    </span>
                                {% endif %}
                            </div>
                        </div>

                        {% if order.expected_delivery_date %}
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Livraison prévue:</strong><br>
                                <span class="text-info">{{ order.expected_delivery_date.strftime('%d/%m/%Y') }}</span>
                                {% if order.expected_delivery_date < now.date() and order.status.value in ['pending', 'partial_received'] %}
                                    <span class="badge bg-danger ms-2">En retard</span>
                                {% endif %}
                            </div>
                            {% if order.actual_delivery_date %}
                            <div class="col-md-6">
                                <strong>Livraison effective:</strong><br>
                                <span class="text-success">{{ order.actual_delivery_date.strftime('%d/%m/%Y') }}</span>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}

                        {% if order.notes %}
                        <div class="row mb-3">
                            <div class="col-12">
                                <strong>Notes:</strong><br>
                                <div class="border p-2 bg-light">{{ order.notes }}</div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Articles de la commande -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-boxes"></i> Articles Commandés ({{ order.items.count() }})</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Article</th>
                                        <th>Quantité</th>
                                        <th>Prix unitaire</th>
                                        {% if order.apply_discount_to_items %}
                                        <th>Prix avant remise</th>
                                        {% endif %}
                                        <th>Total</th>
                                        <th>Reçu</th>
                                        <th>Statut</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in order.items %}
                                    <tr>
                                        <td>
                                            <strong>{{ item.product.name if item.product else item.ingredient.name }}</strong>
                                            {% if item.notes %}
                                                <br><small class="text-muted">{{ item.notes }}</small>
                                            {% endif %}
                                        </td>
                                        <td>{{ item.quantity }} {{ item.unit or '' }}</td>
                                        <td>
                                            <strong>{{ "%.2f"|format(item.unit_price) }} €</strong>
                                            {% if item.original_unit_price and item.original_unit_price != item.unit_price %}
                                                <br><small class="text-muted">Prix référentiel: {{ "%.2f"|format(item.original_unit_price) }} €</small>
                                                {% if order.apply_discount_to_items %}
                                                    <br><small class="text-success">
                                                        <i class="fas fa-tag"></i> Prix après remise: {{ "%.2f"|format(item.unit_price) }} €
                                                    </small>
                                                {% else %}
                                                    <br><small class="text-warning">
                                                        <i class="fas fa-edit"></i> Prix modifié: {{ "%.2f"|format(item.unit_price) }} €
                                                    </small>
                                                {% endif %}
                                            {% endif %}
                                            {% if order.apply_discount_to_items and item.discount_amount and item.discount_amount > 0 %}
                                                <br><small class="text-info">
                                                    <i class="fas fa-percent"></i> Remise unitaire: {{ "%.2f"|format(item.discount_amount) }} €
                                                </small>
                                            {% endif %}
                                        </td>
                                        {% if order.apply_discount_to_items %}
                                        <td>
                                            {% if item.original_unit_price and item.original_unit_price != item.unit_price %}
                                                <span class="text-muted text-decoration-line-through">
                                                    {{ "%.2f"|format(item.original_unit_price) }} €
                                                </span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        {% endif %}
                                        <td>{{ "%.2f"|format(item.total_price) }} €</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if item.received_quantity >= item.quantity else 'warning' if item.received_quantity > 0 else 'secondary' }}">
                                                {{ item.received_quantity or 0 }} / {{ item.quantity }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if item.received_quantity >= item.quantity %}
                                                <span class="text-success"><i class="fas fa-check"></i> Complet</span>
                                            {% elif item.received_quantity > 0 %}
                                                <span class="text-warning"><i class="fas fa-clock"></i> Partiel</span>
                                            {% else %}
                                                <span class="text-secondary"><i class="fas fa-minus"></i> En attente</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    {% if has_discount %}
                                        {% if order.apply_discount_to_items %}
                                            <!-- Remise appliquée aux prix individuels - pas de double soustraction -->
                                            {% set subtotal_before_discount = [] %}
                                            {% for item in order.items %}
                                                {% if item.original_unit_price %}
                                                    {% set _ = subtotal_before_discount.append(item.original_unit_price * item.quantity) %}
                                                {% else %}
                                                    {% set _ = subtotal_before_discount.append(item.total_price) %}
                                                {% endif %}
                                            {% endfor %}
                                            {% set subtotal_before_discount = subtotal_before_discount|sum %}

                                            <tr class="table-light">
                                                <th colspan="4">Sous-total avant remise</th>
                                                <th>{{ "%.2f"|format(subtotal_before_discount) }} €</th>
                                                <th colspan="2"></th>
                                            </tr>
                                            <tr class="table-light">
                                                <th colspan="{{ 5 if order.apply_discount_to_items else 4 }}">
                                                    Remise appliquée aux prix individuels
                                                    {% if order.discount_type == 'percentage' %}
                                                        ({{ "%.2f"|format(order.discount_amount) }}% répartie)
                                                    {% endif %}
                                                </th>
                                                <th class="text-success">
                                                    -{{ "%.2f"|format(subtotal_before_discount - order.subtotal) }} €
                                                </th>
                                                <th colspan="2"></th>
                                            </tr>
                                            <tr class="table-active">
                                                <th colspan="{{ 5 if order.apply_discount_to_items else 4 }}">Total final (prix déjà réduits)</th>
                                                <th>{{ "%.2f"|format(order.subtotal) }} €</th>
                                                <th colspan="2"></th>
                                            </tr>
                                        {% else %}
                                            <!-- Remise appliquée au total -->
                                            <tr class="table-light">
                                                <th colspan="{{ 4 if order.apply_discount_to_items else 3 }}">Sous-total</th>
                                                <th>{{ "%.2f"|format(order.subtotal) }} €</th>
                                                <th colspan="2"></th>
                                            </tr>
                                            <tr class="table-light">
                                                <th colspan="{{ 4 if order.apply_discount_to_items else 3 }}">
                                                    Remise globale (sur total)
                                                    {% if order.discount_type == 'percentage' %}
                                                        ({{ "%.2f"|format(order.discount_amount) }}%)
                                                    {% endif %}
                                                </th>
                                                <th class="text-warning">
                                                    -{% if order.discount_type == 'percentage' %}{{ "%.2f"|format(order.subtotal * order.discount_amount / 100) }}{% else %}{{ "%.2f"|format(order.discount_amount) }}{% endif %} €
                                                </th>
                                                <th colspan="2"></th>
                                            </tr>
                                            <tr class="table-active">
                                                <th colspan="{{ 4 if order.apply_discount_to_items else 3 }}">Total final</th>
                                                <th>{{ "%.2f"|format(order.total_amount) }} €</th>
                                                <th colspan="2"></th>
                                            </tr>
                                        {% endif %}
                                    {% else %}
                                        <!-- Aucune remise -->
                                        <tr class="table-active">
                                            <th colspan="{{ 4 if order.apply_discount_to_items else 3 }}">Total</th>
                                            <th>{{ "%.2f"|format(order.total_amount) }} €</th>
                                            <th colspan="2"></th>
                                        </tr>
                                    {% endif %}
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions et informations complémentaires -->
            <div class="col-lg-4">
                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-cogs"></i> Actions</h6>
                    </div>
                    <div class="card-body">
                        {% if order.status.value == 'pending' %}
                            <button class="btn btn-success btn-sm w-100 mb-2" onclick="markAsReceived({{ order.id }})">
                                <i class="fas fa-check"></i> Marquer comme reçue
                            </button>
                            <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="partialReceive({{ order.id }})">
                                <i class="fas fa-boxes"></i> Réception partielle
                            </button>
                            <!-- Bouton Modifier temporairement désactivé -->
                            <button class="btn btn-outline-secondary btn-sm w-100 mb-2" disabled title="Fonctionnalité en développement">
                                <i class="fas fa-edit"></i> Modifier (bientôt)
                            </button>
                            <button class="btn btn-outline-danger btn-sm w-100 mb-2" onclick="cancelOrder({{ order.id }})">
                                <i class="fas fa-times"></i> Annuler
                            </button>
                        {% elif order.status.value == 'partial_received' %}
                            <button class="btn btn-success btn-sm w-100 mb-2" onclick="markAsReceived({{ order.id }})">
                                <i class="fas fa-check"></i> Finaliser réception
                            </button>
                            <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="partialReceive({{ order.id }})">
                                <i class="fas fa-boxes"></i> Réception partielle
                            </button>
                        {% elif order.status.value == 'received_unpaid' %}
                            <a href="{{ url_for('inventory.supplier_details', id=order.supplier_id) }}" class="btn btn-warning btn-sm w-100 mb-2">
                                <i class="fas fa-credit-card"></i> Aller payer chez le fournisseur
                            </a>
                            <button class="btn btn-outline-warning btn-sm w-100 mb-2" onclick="payPartially({{ order.id }})">
                                <i class="fas fa-coins"></i> Payer partiellement (bientôt)
                            </button>
                        {% endif %}
                        
                        <hr>
                        
                        <button class="btn btn-outline-secondary btn-sm w-100 mb-2" onclick="window.print()">
                            <i class="fas fa-print"></i> Imprimer
                        </button>
                        <button class="btn btn-outline-info btn-sm w-100" onclick="exportOrder({{ order.id }})">
                            <i class="fas fa-download"></i> Exporter PDF
                        </button>
                    </div>
                </div>

                <!-- Historique -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-history"></i> Historique</h6>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Commande créée</h6>
                                    <p class="timeline-text">{{ order.created_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                                </div>
                            </div>
                            
                            {% if order.status.value in ['partial_received', 'received'] %}
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Première réception</h6>
                                    <p class="timeline-text">{{ order.updated_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if order.status.value == 'received' and order.actual_delivery_date %}
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Commande complète</h6>
                                    <p class="timeline-text">{{ order.actual_delivery_date.strftime('%d/%m/%Y') }}</p>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if order.status.value == 'cancelled' %}
                            <div class="timeline-item">
                                <div class="timeline-marker bg-danger"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Commande annulée</h6>
                                    <p class="timeline-text">{{ order.updated_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Informations fournisseur -->
                {% if order.supplier %}
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-truck"></i> Fournisseur</h6>
                    </div>
                    <div class="card-body">
                        <h6>{{ order.supplier.name }}</h6>
                        {% if order.supplier.contact_name %}
                            <p class="mb-1"><strong>Contact:</strong> {{ order.supplier.contact_name }}</p>
                        {% endif %}
                        {% if order.supplier.phone %}
                            <p class="mb-1"><strong>Téléphone:</strong> {{ order.supplier.phone }}</p>
                        {% endif %}
                        {% if order.supplier.email %}
                            <p class="mb-1"><strong>Email:</strong> {{ order.supplier.email }}</p>
                        {% endif %}
                        <a href="{{ url_for('inventory.supplier_details', id=order.supplier.id) }}" class="btn btn-outline-primary btn-sm mt-2">
                            <i class="fas fa-eye"></i> Voir fournisseur
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-title {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.timeline-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0;
}
</style>

<script>
// Fonctions pour les actions sur la commande
function markAsReceived(orderId) {
    if (confirm('Êtes-vous sûr de vouloir marquer cette commande comme entièrement reçue ?')) {
        fetch(`/inventory/stock-replenishment/orders/${orderId}/receive`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande marquée comme reçue et stock mis à jour');
                location.reload();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    }
}

function partialReceive(orderId) {
    window.location.href = `/inventory/stock-replenishment/pending-orders#order-${orderId}`;
}

function cancelOrder(orderId) {
    if (confirm('Êtes-vous sûr de vouloir annuler cette commande ?')) {
        fetch(`/inventory/stock-replenishment/orders/${orderId}/cancel`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande annulée');
                location.reload();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    }
}

function exportOrder(orderId) {
    window.open(`/inventory/stock-replenishment/orders/${orderId}/export`, '_blank');
}
</script>
{% endblock %}
