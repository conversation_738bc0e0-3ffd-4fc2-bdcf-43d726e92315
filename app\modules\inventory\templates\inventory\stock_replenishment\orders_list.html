{% extends "base.html" %}

{% block title %}Liste des Commandes d'Achat{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-3">
                <h2 class="mb-0">
                    <i class="fas fa-list"></i> 
                    Liste des Commandes d'Achat
                </h2>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.stock_replenishment_pos_mode') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-cash-register"></i> Mode POS
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_form_mode') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-edit"></i> Mode Formulaire
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-plus"></i> Nouvelle Commande
                    </a>
                    <a href="{{ url_for('inventory.pending_orders') }}" class="btn btn-outline-warning btn-sm me-2">
                        <i class="fas fa-clock"></i> Pending Marchandise
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Filtres -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Fournisseur</label>
                        <select class="form-select" name="supplier_id">
                            <option value="">Tous les fournisseurs</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}" {{ 'selected' if request.args.get('supplier_id') == supplier.id|string }}>
                                {{ supplier.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Statut</label>
                        <select class="form-select" name="status">
                            <option value="">Tous les statuts</option>
                            <option value="pending" {{ 'selected' if request.args.get('status') == 'pending' }}>En attente</option>
                            <option value="partial_received" {{ 'selected' if request.args.get('status') == 'partial_received' }}>Partiellement reçue</option>
                            <option value="received" {{ 'selected' if request.args.get('status') == 'received' }}>Reçue</option>
                            <option value="cancelled" {{ 'selected' if request.args.get('status') == 'cancelled' }}>Annulée</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Date de commande</label>
                        <input type="date" class="form-control" name="order_date" value="{{ request.args.get('order_date', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Actions</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-filter"></i> Filtrer
                            </button>
                            <a href="{{ url_for('inventory.purchase_orders_list') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Effacer
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Liste des commandes -->
        {% if orders.items %}
            <div class="row">
                {% for order in orders.items %}
                <div class="col-lg-6 mb-4">
                    <div class="card border-{{ 'success' if order.status.value == 'received' else 'warning' if order.status.value == 'partial_received' else 'primary' if order.status.value == 'pending' else 'danger' }}">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-alt"></i> 
                                    {{ order.reference }}
                                </h6>
                                <span class="badge bg-{{ 'success' if order.status.value == 'received' else 'warning' if order.status.value == 'partial_received' else 'primary' if order.status.value == 'pending' else 'danger' }}">
                                    {% if order.status.value == 'pending' %}En attente
                                    {% elif order.status.value == 'partial_received' %}Partiellement reçue
                                    {% elif order.status.value == 'received' %}Reçue
                                    {% elif order.status.value == 'cancelled' %}Annulée
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>Fournisseur:</strong><br>
                                    <span class="text-primary">{{ order.supplier_name }}</span>
                                </div>
                                <div class="col-6">
                                    <strong>Date de commande:</strong><br>
                                    {{ order.order_date.strftime('%d/%m/%Y') }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>Montant total:</strong><br>
                                    <span class="h6 text-success">{{ "%.2f"|format(order.total_amount) }} €</span>
                                </div>
                                <div class="col-6">
                                    <strong>Articles:</strong><br>
                                    {{ order.items.count() }} article(s)
                                </div>
                            </div>

                            {% if order.expected_delivery_date %}
                            <div class="row mb-3">
                                <div class="col-12">
                                    <strong>Livraison prévue:</strong><br>
                                    <span class="text-info">{{ order.expected_delivery_date.strftime('%d/%m/%Y') }}</span>
                                    {% if order.expected_delivery_date < now.date() and order.status.value in ['pending', 'partial_received'] %}
                                        <span class="badge bg-danger ms-2">En retard</span>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}

                            {% if order.actual_delivery_date %}
                            <div class="row mb-3">
                                <div class="col-12">
                                    <strong>Livraison effective:</strong><br>
                                    <span class="text-success">{{ order.actual_delivery_date.strftime('%d/%m/%Y') }}</span>
                                </div>
                            </div>
                            {% endif %}

                            {% if order.notes %}
                            <div class="row mb-3">
                                <div class="col-12">
                                    <strong>Notes:</strong><br>
                                    <small class="text-muted">{{ order.notes[:100] }}{% if order.notes|length > 100 %}...{% endif %}</small>
                                </div>
                            </div>
                            {% endif %}

                            <!-- Actions -->
                            <div class="d-flex gap-2 flex-wrap">
                                <a href="{{ url_for('inventory.purchase_order_details', order_id=order.id) }}" 
                                   class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-eye"></i> Détails
                                </a>
                                
                                {% if order.status.value == 'pending' %}
                                    <button class="btn btn-success btn-sm" onclick="markAsReceived({{ order.id }})">
                                        <i class="fas fa-check"></i> Marquer reçue
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" onclick="partialReceive({{ order.id }})">
                                        <i class="fas fa-boxes"></i> Réception partielle
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" onclick="editOrder({{ order.id }})">
                                        <i class="fas fa-edit"></i> Modifier
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="cancelOrder({{ order.id }})">
                                        <i class="fas fa-times"></i> Annuler
                                    </button>
                                {% elif order.status.value == 'partial_received' %}
                                    <button class="btn btn-success btn-sm" onclick="markAsReceived({{ order.id }})">
                                        <i class="fas fa-check"></i> Finaliser réception
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" onclick="partialReceive({{ order.id }})">
                                        <i class="fas fa-boxes"></i> Réception partielle
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-footer text-muted">
                            <small>
                                Créée le {{ order.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                {% if order.updated_at != order.created_at %}
                                    • Modifiée le {{ order.updated_at.strftime('%d/%m/%Y à %H:%M') }}
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if orders.pages > 1 %}
            <nav aria-label="Navigation des commandes">
                <ul class="pagination justify-content-center">
                    {% if orders.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.purchase_orders_list', page=orders.prev_num, **request.args) }}">
                            Précédent
                        </a>
                    </li>
                    {% endif %}

                    {% for page in range(1, orders.pages + 1) %}
                        {% if page <= 3 or page > orders.pages - 3 or (page >= orders.page - 1 and page <= orders.page + 1) %}
                        <li class="page-item {{ 'active' if page == orders.page else '' }}">
                            <a class="page-link" href="{{ url_for('inventory.purchase_orders_list', page=page, **request.args) }}">
                                {{ page }}
                            </a>
                        </li>
                        {% elif page == 4 and orders.page > 5 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% elif page == orders.pages - 3 and orders.page < orders.pages - 4 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if orders.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.purchase_orders_list', page=orders.next_num, **request.args) }}">
                            Suivant
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center text-muted py-5">
                <i class="fas fa-clipboard-list fa-4x mb-3"></i>
                <h4>Aucune commande trouvée</h4>
                <p>Aucune commande ne correspond aux critères de recherche ou vous n'avez pas encore créé de commandes.</p>
                <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Créer une nouvelle commande
                </a>
            </div>
        {% endif %}
    </div>
</div>

<script>
// Fonctions pour les actions sur les commandes
function markAsReceived(orderId) {
    if (confirm('Êtes-vous sûr de vouloir marquer cette commande comme entièrement reçue ?')) {
        fetch(`/inventory/stock-replenishment/orders/${orderId}/receive`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande marquée comme reçue et stock mis à jour');
                location.reload();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    }
}

function partialReceive(orderId) {
    window.location.href = `/inventory/stock-replenishment/pending-orders#order-${orderId}`;
}

function editOrder(orderId) {
    window.location.href = `/inventory/stock-replenishment/orders/${orderId}/edit`;
}

function cancelOrder(orderId) {
    if (confirm('Êtes-vous sûr de vouloir annuler cette commande ?')) {
        fetch(`/inventory/stock-replenishment/orders/${orderId}/cancel`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande annulée');
                location.reload();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    }
}
</script>
{% endblock %}
